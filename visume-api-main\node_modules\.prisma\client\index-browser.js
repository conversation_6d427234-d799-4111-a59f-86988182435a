
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.AnalyticsScalarFieldEnum = {
  interaction_id: 'interaction_id',
  employer_id: 'employer_id',
  profile_id: 'profile_id',
  interaction_type: 'interaction_type',
  timestamp: 'timestamp'
};

exports.Prisma.CompanyScalarFieldEnum = {
  id: 'id',
  company_name: 'company_name',
  company_description: 'company_description',
  company_website: 'company_website',
  company_logo: 'company_logo',
  gst: 'gst',
  superuser: 'superuser',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.EmployerScalarFieldEnum = {
  id: 'id',
  emp_id: 'emp_id',
  emp_name: 'emp_name',
  emp_email: 'emp_email',
  emp_mobile: 'emp_mobile',
  company_id: 'company_id',
  designation: 'designation',
  created_at: 'created_at'
};

exports.Prisma.EmployerplansScalarFieldEnum = {
  id: 'id',
  emp_id: 'emp_id',
  plan_id: 'plan_id',
  start_date: 'start_date',
  end_date: 'end_date',
  creditsLeft: 'creditsLeft'
};

exports.Prisma.EmployerprofilesScalarFieldEnum = {
  id: 'id',
  emp_id: 'emp_id',
  video_resume_id: 'video_resume_id',
  status: 'status',
  shortlisted_at: 'shortlisted_at',
  unlocked_at: 'unlocked_at'
};

exports.Prisma.Job_descriptionsScalarFieldEnum = {
  id: 'id',
  employer_id: 'employer_id',
  suggested_profiles: 'suggested_profiles',
  JobDescription: 'JobDescription',
  timestamp: 'timestamp'
};

exports.Prisma.JobseekerScalarFieldEnum = {
  id: 'id',
  cand_id: 'cand_id',
  cand_name: 'cand_name',
  cand_mobile: 'cand_mobile',
  cand_email: 'cand_email',
  marks: 'marks',
  gender: 'gender',
  languages_known: 'languages_known',
  cand_skills: 'cand_skills',
  profile_picture: 'profile_picture',
  stripped_resume: 'stripped_resume',
  preferred_location: 'preferred_location',
  created_at: 'created_at'
};

exports.Prisma.JobseekerplansScalarFieldEnum = {
  id: 'id',
  cand_id: 'cand_id',
  plan_id: 'plan_id',
  start_date: 'start_date',
  end_date: 'end_date',
  is_profile_forwarded: 'is_profile_forwarded',
  is_profile_promoted: 'is_profile_promoted',
  review_count: 'review_count',
  credits: 'credits'
};

exports.Prisma.Mock_interviewScalarFieldEnum = {
  id: 'id',
  cand_id: 'cand_id',
  mock_interview_id: 'mock_interview_id',
  role: 'role',
  skills: 'skills',
  job_type: 'job_type',
  experience_range: 'experience_range',
  salary: 'salary',
  questions: 'questions',
  score: 'score',
  status: 'status',
  created_at: 'created_at'
};

exports.Prisma.PlansScalarFieldEnum = {
  id: 'id',
  plan_name: 'plan_name',
  plan_description: 'plan_description',
  plan_price: 'plan_price',
  plan_duration_days: 'plan_duration_days',
  role: 'role',
  credits_assigned: 'credits_assigned',
  features: 'features',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.SuggestedjobsScalarFieldEnum = {
  id: 'id',
  title: 'title',
  company: 'company',
  openings: 'openings',
  url: 'url',
  image: 'image'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  role: 'role',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.VideoprofileScalarFieldEnum = {
  id: 'id',
  cand_id: 'cand_id',
  video_profile_id: 'video_profile_id',
  role: 'role',
  skills: 'skills',
  job_type: 'job_type',
  experience_range: 'experience_range',
  salary: 'salary',
  questions: 'questions',
  video_url: 'video_url',
  score: 'score',
  status: 'status',
  created_at: 'created_at'
};

exports.Prisma.RolesScalarFieldEnum = {
  id: 'id',
  role_name: 'role_name'
};

exports.Prisma.SkillsScalarFieldEnum = {
  id: 'id',
  skill_name: 'skill_name'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.companyOrderByRelevanceFieldEnum = {
  company_name: 'company_name',
  company_description: 'company_description',
  company_website: 'company_website',
  company_logo: 'company_logo',
  gst: 'gst'
};

exports.Prisma.employerOrderByRelevanceFieldEnum = {
  emp_name: 'emp_name',
  emp_email: 'emp_email',
  designation: 'designation'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.jobseekerOrderByRelevanceFieldEnum = {
  cand_id: 'cand_id',
  cand_name: 'cand_name',
  cand_mobile: 'cand_mobile',
  cand_email: 'cand_email',
  marks: 'marks',
  languages_known: 'languages_known',
  cand_skills: 'cand_skills',
  profile_picture: 'profile_picture',
  stripped_resume: 'stripped_resume',
  preferred_location: 'preferred_location'
};

exports.Prisma.mock_interviewOrderByRelevanceFieldEnum = {
  cand_id: 'cand_id',
  role: 'role',
  skills: 'skills',
  salary: 'salary',
  questions: 'questions',
  score: 'score'
};

exports.Prisma.plansOrderByRelevanceFieldEnum = {
  plan_name: 'plan_name',
  plan_description: 'plan_description',
  features: 'features'
};

exports.Prisma.suggestedjobsOrderByRelevanceFieldEnum = {
  title: 'title',
  company: 'company',
  url: 'url',
  image: 'image'
};

exports.Prisma.userOrderByRelevanceFieldEnum = {
  email: 'email',
  password: 'password'
};

exports.Prisma.videoprofileOrderByRelevanceFieldEnum = {
  cand_id: 'cand_id',
  role: 'role',
  skills: 'skills',
  salary: 'salary',
  questions: 'questions',
  video_url: 'video_url',
  score: 'score'
};

exports.Prisma.rolesOrderByRelevanceFieldEnum = {
  role_name: 'role_name'
};

exports.Prisma.skillsOrderByRelevanceFieldEnum = {
  skill_name: 'skill_name'
};
exports.analytics_interaction_type = exports.$Enums.analytics_interaction_type = {
  view: 'view',
  click: 'click'
};

exports.employerprofiles_status = exports.$Enums.employerprofiles_status = {
  shortlisted: 'shortlisted',
  unlocked: 'unlocked'
};

exports.jobseeker_gender = exports.$Enums.jobseeker_gender = {
  male: 'male',
  female: 'female',
  other: 'other'
};

exports.mock_interview_job_type = exports.$Enums.mock_interview_job_type = {
  startup: 'startup',
  mnc: 'mnc'
};

exports.mock_interview_experience_range = exports.$Enums.mock_interview_experience_range = {
  ZeroToOne: 'ZeroToOne',
  TwoToThree: 'TwoToThree',
  ThreeToFive: 'ThreeToFive'
};

exports.mock_interview_status = exports.$Enums.mock_interview_status = {
  active: 'active',
  inactive: 'inactive',
  start: 'start'
};

exports.plans_role = exports.$Enums.plans_role = {
  js: 'js',
  emp: 'emp'
};

exports.user_role = exports.$Enums.user_role = {
  jobseeker: 'jobseeker',
  employer: 'employer'
};

exports.videoprofile_job_type = exports.$Enums.videoprofile_job_type = {
  startup: 'startup',
  mnc: 'mnc'
};

exports.videoprofile_experience_range = exports.$Enums.videoprofile_experience_range = {
  ZeroToOne: 'ZeroToOne',
  TwoToThree: 'TwoToThree',
  ThreeToFive: 'ThreeToFive'
};

exports.videoprofile_status = exports.$Enums.videoprofile_status = {
  active: 'active',
  inactive: 'inactive',
  started: 'started',
  notsubmitted: 'notsubmitted'
};

exports.Prisma.ModelName = {
  analytics: 'analytics',
  company: 'company',
  employer: 'employer',
  employerplans: 'employerplans',
  employerprofiles: 'employerprofiles',
  job_descriptions: 'job_descriptions',
  jobseeker: 'jobseeker',
  jobseekerplans: 'jobseekerplans',
  mock_interview: 'mock_interview',
  plans: 'plans',
  suggestedjobs: 'suggestedjobs',
  user: 'user',
  videoprofile: 'videoprofile',
  roles: 'roles',
  skills: 'skills'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
